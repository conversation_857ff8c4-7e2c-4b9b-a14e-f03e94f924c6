const path = require('path');
const crypto = require('crypto');
const express = require('express');
const { Telegraf } = require('telegraf');
require('dotenv').config();

const BOT_TOKEN = process.env.BOT_TOKEN;
const WEBAPP_URL = process.env.WEBAPP_URL || 'http://localhost:3000/app';
if (!BOT_TOKEN) throw new Error('Missing BOT_TOKEN in .env');

const app = express();
app.use(express.json());
app.use('/app', express.static(path.join(__dirname, 'app')));

// Verify Telegram initData from the Mini App
function verifyTelegramInitData(initData, botToken) {
  try {
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get('hash');
    urlParams.delete('hash');

    // Build data_check_string
    const dataCheck = [];
    // sort by key, exact order matters
    [...urlParams.entries()].sort(([a],[b]) => a.localeCompare(b))
      .forEach(([k, v]) => dataCheck.push(`${k}=${v}`));
    const dataCheckString = dataCheck.join('\n');

    // secret_key = HMAC key = sha256 of bot token
    const secretKey = crypto.createHash('sha256').update(botToken).digest();
    const hmac = crypto.createHmac('sha256', secretKey).update(dataCheckString).digest('hex');

    // constant-time compare
    return crypto.timingSafeEqual(Buffer.from(hmac), Buffer.from(hash));
  } catch (e) {
    return false;
  }
}

app.post('/api/me', (req, res) => {
  const initData = req.body.initData || '';
  if (!verifyTelegramInitData(initData, BOT_TOKEN)) {
    return res.status(403).json({ ok: false, error: 'bad initData' });
  }
  const params = Object.fromEntries(new URLSearchParams(initData));
  const user = params.user ? JSON.parse(params.user) : null;
  return res.json({ ok: true, user });
});

// Bot: sends a WebApp button and receives data from Mini App
const bot = new Telegraf(BOT_TOKEN);

bot.start((ctx) => {
  return ctx.reply('Open the Mini App:', {
    reply_markup: {
      inline_keyboard: [[
        { text: 'Open Mini App', web_app: { url: WEBAPP_URL } }
      ]]
    }
  });
});

// Receive data from Mini App (Telegram.WebApp.sendData)
bot.on('message', async (ctx) => {
  const wad = ctx.message?.web_app_data;
  if (wad) {
    try {
      const data = JSON.parse(wad.data);
      await ctx.reply('Received from Mini App: ' + JSON.stringify(data));
    } catch {
      await ctx.reply('Received from Mini App: ' + wad.data);
    }
  }
});

async function start() {
  const port = process.env.PORT || 3000;
  app.listen(port, () => console.log('Web server on http://localhost:' + port));
  await bot.launch();
  console.log('Bot launched');
}
start();

process.once('SIGINT', () => bot.stop('SIGINT'));
process.once('SIGTERM', () => bot.stop('SIGTERM'));