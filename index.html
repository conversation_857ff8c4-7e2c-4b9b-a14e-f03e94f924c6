<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1, maximum-scale=1" />
  <title>My Mini App</title>
  <script src="https://telegram.org/js/telegram-web-app.js"></script>
  <style>
    body { font-family: system-ui, sans-serif; margin: 16px; color: var(--tg-theme-text-color, #222); background: var(--tg-theme-bg-color, #fff); }
    button { padding: 10px 14px; border-radius: 8px; border: 0; background: #2ea6ff; color: #fff; font-weight: 600; }
    .card { padding: 12px; border-radius: 10px; background: var(--tg-theme-secondary-bg-color, #f3f3f3); }
  </style>
</head>
<body>
  <h2>Welcome to My Mini App</h2>
  <div class="card">
    <div id="user">Loading user…</div>
  </div>
  <p style="margin-top:12px">
    <button id="ping">Ping bot</button>
  </p>

  <script>
    const tg = window.Telegram.WebApp;
    tg.ready();       // Notify Telegram we're ready
    tg.expand();      // Use as much height as possible

    // Theme
    document.body.style.background = tg.themeParams?.bg_color || '';
    document.body.style.color = tg.themeParams?.text_color || '';

    // Show user
    const user = tg.initDataUnsafe?.user;
    const userEl = document.getElementById('user');
    userEl.textContent = user ? `Hello, ${user.first_name} (@${user.username || 'no username'})`
                              : 'No user found (are you in Telegram?)';

    // Optional: call your backend securely by verifying initData on server
    async function getProfile() {
      const res = await fetch('/api/me', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ initData: tg.initData }) // signed string from Telegram
      });
      const json = await res.json();
      console.log('Profile response:', json);
    }
    getProfile().catch(console.error);

    // Main button example
    tg.MainButton.setText('Send data to bot');
    tg.MainButton.onClick(() => {
      tg.sendData(JSON.stringify({ action: 'ping', ts: Date.now() })); // arrives to bot as web_app_data
      tg.close(); // Optional: close after sending data
    });
    tg.MainButton.show();

    // Or a normal in-page button:
    document.getElementById('ping').addEventListener('click', () => {
      tg.HapticFeedback?.impactOccurred('light');
      tg.sendData(JSON.stringify({ action: 'ping', source: 'button' }));
    });
  </script>
</body>
</html>